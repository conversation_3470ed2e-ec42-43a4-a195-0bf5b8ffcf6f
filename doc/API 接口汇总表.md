
## 接口概览  
  
| 序号 | 接口分类 | 接口名称 | URL路径 | 请求参数类型 | 响应类型 | 描述 |  
|------|----------|----------|---------|--------------|----------|------|  
| 1 | 客户管理 | CustomerSaveRequest | `/v2/gjwl/basedata/bd_customer/saveCustomer` | `List<Customer>` | `CustomerSaveResponse` | 保存客户信息 |  
| 2 | 客户管理 | CustomerSaveExtRequest | `/v2/gjwl/cas/basedata/bd_customer/saveCustomer` | `List<Customer>` | `CustomerSaveExtResponse` | 客户扩展保存 |  
| 3 | 客户管理 | CustomerUnAuditRequest | `/v2/gjwl/basedata/bd_customer/unAuditCustomer` | `CustomerUnAudit` | `CustomerUnAuditResponse` | 客户反审核 |  
| 4 | 供应商管理 | SupplierCreateRequest | `/v2/basedata/bd_supplier/add` | `SupplierCreatePara` | `SimpleResponse` | 创建供应商 |  
| 5 | 供应商管理 | SupplierSaveRequest | `/v2/gjwl/cas/basedata/bd_supplier/saveSupplier` | `SupplierSavePara` | `SupplierSaveResponse` | 保存供应商 |  
| 6 | 供应商管理 | SupplierUpdateRequest | `/v2/basedata/bd_supplier/batchUpdate` | `List<SupplierUpdatePara>` | `SimpleResponse` | 批量更新供应商 |  
| 7 | 供应商管理 | SupplierUnAuditRequest | `/v2/gjwl/basedata/bd_supplier/unAuditSupplier` | `UnAuditPara` | `SupplierUnAuditResponse` | 供应商反审核 |  
| 8 | 收款管理 | PaymentRequest | `/v2/gjwl/cas/cas_recbill/saveRecBill` | `List<PaymentPara>` | `PaymentResponse` | 收款单新增保存 |  
| 9 | 收款管理 | ReceiptRecordQueryRequest | `/v2/gjwl/cas/transDetail/transDetailNewQuery` | `ReceiptRecordQueryParams` | `DetailQueryResponse` | 收款流水查询 |  
| 10 | 收款管理 | ReceiptClaimParamsRequest | `/v2/gjwl/cas/recClaimInterface/recClaimInterface` | `ReceiptClaimParams` | `ReceiptClaimResponse` | 收款流水认领 |  
| 11 | 收款管理 | CancelClaimRequest | `/v2/gjwl/cas/cancelRecClaim/calcelrecclaiminterface` | `CancelClaimParams` | `ReceiptClaimResponse` | 取消认领 |  
| 12 | 付款管理 | PayApplyQueryRequest | `/v2/gjwl/ap/payApply/payApplyQuery` | `PayApplyQueryParam` | `PayApplyQueryResponse` | 付款申请查询 |  
| 13 | 付款管理 | PayApplyAutoCreateRequest | `/v2/gjwl/ap/payApply/payApplyAutoCreate` | `List<Claim>` | `PayApplyAutoCreateResponse` | 付款申请自动创建 |  
| 14 | 付款管理 | AddKdPaymentRequest | `/v2/gjwl/cas/cas_paybill/savePayBill` | `List<AddKdPaymentPara>` | `AddKdPaymentResponse` | 金蝶付款单新增 |  
| 15 | 付款管理 | ClaimQueryRequest | `/v2/gjwl/cas/billSatus/billstatusquery` | `ClaimQueryPara` | `PayLineResultResponse` | 查询报账单状态 |
| 16 | 项目管理 | ProjectSaveRequest | `/v2/gjwl/basedata/bd_project/saveProject` | `List<Project>` | `ProjectSaveResponse` | 项目保存提交审核 |
| 17 | 项目管理 | ProjectUnAuditRequest | `/v2/basedata/bd_project/batchUnaudit` | `ProjectUnAudit` | `ProjectUnAuditResponse` | 项目反审核 |
| 18 | 物料管理 | MaterialSaveRequest | `/v2/gjwl/gjwl_basedata_ext/saveMaterial` | `MaterialSaveRequest.MaterialSaveData` | `MaterialSaveResponse` | 物料批量保存提交审核 |
| 19 | 物料管理 | MaterialInventoryUpdateRequest | `/v2/sbd/bd_materialinventoryinfo/batchUpdate` | `List<MaterialInventoryUpdatePara>` | `MaterialInventoryUpdateResponse` | 物料库存信息批量更新 |

## 详细参数说明
  
### 客户管理接口参数  
  
#### Customer (客户信息)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| number | String | 是 | 客户编码 |  
| name | String | 是 | 客户名称 |  
| ... | ... | ... | 其他客户属性 |  
  
#### CustomerUnAudit (客户反审核参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| customerId | String | 是 | 客户ID |  
| reason | String | 否 | 反审核原因 |  
  
### 供应商管理接口参数  
  
#### SupplierCreatePara (供应商创建参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| number | String | 是 | 供应商编码 |  
| name | String | 是 | 供应商名称 |  
| entryLinkman | List<Linkman> | 否 | 联系人分录 |  
| entryBank | List<BankInfo> | 否 | 银行信息分录 |  
| entryGroupStandard | List<GroupStandard> | 否 | 分类标准 |  
| entryTax | List<TaxQualification> | 否 | 税务资质 |  
  
#### SupplierSavePara (供应商保存参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| number | String | 是 | 供应商编码 |  
| gjwlAppid | String | 是 | 应用ID |  
| name | String | 是 | 供应商名称 |  
  
### 收款管理接口参数  
  
#### PaymentPara (收款单参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| billNo | String | 是 | 单据编号 |  
| bizDate | LocalDate | 是 | 业务日期 |  
| payerType | String | 是 | 付款单位类型 |  
| txtDescription | String | 否 | 摘要 |  
| actRecAmt | BigDecimal | 是 | 收款金额 |  
| exchangeRate | BigDecimal | 是 | 汇率 |  
| localAmt | BigDecimal | 是 | 折本位币 |  
| payerName | String | 否 | 付款单位名称 |  
| payerAcctBankNum | String | 否 | 付款账户 |  
| settleTNumber | String | 否 | 结算号 |  
| payer | Long | 否 | 付款单位ID |  
| payerFormId | String | 否 | 付款单位类型标识ID |  
| payerAccFormId | String | 否 | 付款账户类型标识ID |  
| payerAcctBank | Long | 否 | 付款账户ID |  
| billTypeId | Long | 否 | 单据类型ID |  
| billTypeNumber | String | 否 | 单据类型编码 |  
| payerBankName | String | 否 | 付款银行 |  
| isAgent | Boolean | 否 | 代理收款 |  
| paymentMode | String | 否 | 付款方式 |  
| isRefund | Boolean | 否 | 退款退票业务 |  
| isFullRefund | Boolean | 否 | 全额退款 |  
| exRateDate | LocalDate | 否 | 汇率日期 |  
| entries | List<Entry> | 否 | 收款明细 |  
| entryEntities | List<EntryEntity> | 否 | 单据体列表 |  
| infoEntries | List<InfoEntry> | 否 | 流水信息列表 |  
| draftBills | List<DraftBill> | 否 | 结算号基础资料列表 |  
| orgId | Long | 否 | 收款组织ID |  
| orgNumber | String | 否 | 收款组织编码 |  
| payeeBankId | Long | 否 | 收款银行ID |  
| payeeBankNumber | String | 否 | 收款银行编码 |  
  
#### ReceiptRecordQueryParams (收款流水查询参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| compCode | String | 是 | 公司编码 |  
  
#### ReceiptClaimParams (收款流水认领参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| compCode | String | 是 | 公司编码 |  
| claimRevInforBean | ReceiptClaimItem | 是 | 认领信息 |  
  
#### ReceiptClaimItem (认领信息)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| receivablesId | Long | 是 | 收款流水ID |  
| receivablesNum | String | 是 | 收款流水编号 |  
  
#### CancelClaimParams (取消认领参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| compCode | String | 是 | 公司编码 |  
| operateType | String | 是 | 操作类型 (UNCLAIM取消认领, AUDIT认领复核) |  
| receivablesId | Long | 是 | 收款流水ID |  
| remark | String | 否 | 操作原因 |  
| casRecBillId | String | 是 | 金蝶收款单据ID |  
  
### 付款管理接口参数  
  
#### PayApplyQueryParam (付款申请查询参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| ... | ... | ... | 查询条件字段 |  
  
#### AddKdPaymentPara (金蝶付款单参数)  
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| billno | String | 是 | 单据编号 |  
| bizdate | Date | 是 | 业务日期 |  
| entry | List<AddKdPaymentDetail> | 是 | 分录 |  
| payeetype | String | 是 | 收款单位类型 |  
| description | String | 否 | 描述 |  
  
#### ClaimQueryPara (报账单状态查询参数)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| claimNos | String | 是 | 报账单号(多个用逗号分隔) |

### 项目管理接口参数

#### Project (项目信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 项目ID (修改时需传) |
| number | String | 是 | 项目编码 |
| name | String | 是 | 项目名称 |
| createorg_number | String | 是 | 创建组织编码 |
| gjwl_appid | String | 是 | 外部编码 |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |

#### ProjectUnAudit (项目反审核参数)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | List<String> | 是 | 项目ID列表 |

### 物料管理接口参数

#### MaterialSaveRequest.MaterialSaveData (物料保存请求数据)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| materials | List<MaterialDataVo> | 否 | 物料数据列表 |

#### MaterialDataVo (物料数据)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| materialInfoVo | MaterialInfoVo | 否 | 物料主档信息 |
| commonInfoVo | CommonInfoVo | 否 | 物料组织公共信息 |

#### MaterialInfoVo (物料主档信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 物料ID |
| name | String | 是 | 物料名称 |
| number | String | 否 | 物料编码 |
| gjwl_appid | String | 是 | 外部编码 |
| createorg | String | 是 | 创建组织编码(固定值"gjwl") |
| baseunit | String | 是 | 基本单位编码 |
| modelnum | String | 否 | 规格型号 |
| unitconvertdir | String | 否 | 换算方向(固定值"A") |
| entry_groupstandard | List<GroupStandardEntry> | 否 | 分类信息分录 |

#### CommonInfoVo (物料组织公共信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| materialtype | String | 是 | 物料类型(固定值"1") |
| materialattr | String | 是 | 物料属性(固定值"10040") |
| enablepur | Boolean | 否 | 采购信息(固定值true) |
| enablesale | Boolean | 否 | 销售信息(固定值true) |
| enableinv | Boolean | 否 | 库存信息(固定值true) |
| group | String | 否 | 存货类别(固定值"CHJZFL06-SYS") |

#### MaterialInventoryUpdatePara (物料库存更新参数)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| enableshelflifemgr | Boolean | 是 | 保质期管理 |
| shelflifeunit | String | 是 | 保质期单位(day/month/year) |
| shelflife | Integer | 否 | 保质期 |
| caldirection | String | 否 | 计算方向(固定值"4") |
| startdatecaltype | String | 否 | 生产日计算方式(固定值"1") |
| calculationforenddate | String | 是 | 到期日计算方式(固定值"0") |
| leadtimeunit | String | 是 | 提前期单位(day/month/year) |
| masterid_number | String | 是 | 物料编码 |
| createorg_number | String | 是 | 库存信息创建组织编码(固定值"gjwl") |

## 响应格式说明
  
### 标准响应格式  
```json  
{  
  "errorCode": "0",  
  "message": "成功",  
  "status": true,  
  "data": {}  
}  
```  
  
### 扩展响应格式  
```json  
{  
  "errorCode": "0",  
  "message": "成功",   
"status": true,  
  "data": [  
    {  
      "success": true,  
      "message": "操作成功"  
    }  
  ]  
}  
```  
  
## 日志模块说明  
  
| 接口 | 日志模块 | 描述 |  
|------|----------|------|  
| CustomerSaveRequest | kd.bd_customer.saveCustomer | 客户保存日志 |  
| CustomerSaveExtRequest | kd.bd_customer.saveCustomerExt | 客户扩展保存日志 |  
| CustomerUnAuditRequest | kd.bd_customer.unAuditCustomer | 客户反审核日志 |  
| SupplierCreateRequest | kd.bd_supplier.add | 供应商创建日志 |  
| SupplierSaveRequest | kd.bd_supplier.saveSupplier | 供应商保存日志 |  
| SupplierUpdateRequest | kd.bd_supplier.batchUpdate | 供应商批量更新日志 |  
| SupplierUnAuditRequest | kd.bd_customer.unAuditSupplier | 供应商反审核日志 |  
| PaymentRequest | kd.cas_recbill.saveRecBill | 收款单保存日志 |  
| ReceiptRecordQueryRequest | kd.bd_receipt.receiptRecordQuery | 收款流水查询日志 |  
| ReceiptClaimParamsRequest | kd.bd_receipt.receiptClaim | 收款流水认领日志 |  
| CancelClaimRequest | kd.bd_receipt.cancelClaim | 取消认领日志 |  
| PayApplyQueryRequest | kd.payApply.query | 付款申请查询日志 |  
| PayApplyAutoCreateRequest | kd.payApply.create | 付款申请创建日志 |  
| AddKdPaymentRequest | kd.cas_paybill.addSave | 金蝶付款单新增日志 |
| ClaimQueryRequest | kd.cas.billSatus.billStatusQuery | 报账单状态查询日志 |
| ProjectSaveRequest | kd.bd_project.saveProject | 项目保存日志 |
| ProjectUnAuditRequest | kd.bd_project.unAuditProject | 项目反审核日志 |
| MaterialSaveRequest | kd.bd_material.saveMaterial | 物料保存日志 |
| MaterialInventoryUpdateRequest | kd.bd_material.inventoryUpdate | 物料库存更新日志 |

## 特殊说明
  
### 标准格式包装  
- 大部分接口使用标准格式包装 (standard() = true)  
- 以下接口不使用标准格式包装 (standard() = false):  
  - ReceiptRecordQueryRequest  
  - ReceiptClaimParamsRequest    
  - CancelClaimRequest  
  - ClaimQueryRequest  
  
### 请求头要求  
- Content-Type: application/json  
- accesstoken: 访问令牌  
- x-acgw-identity: 身份标识  
- Idempotency-Key: 幂等性键值(UUID)  
  
### 超时配置  
- 连接超时: 60秒  
- 读取超时: 60秒  
- 写入超时: 60秒