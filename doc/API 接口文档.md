 
## 概述  
  
本文档描述了金蝶SDK中所有可用的API接口，包括接口名称、URL路径、请求参数和返回参数。  
  
## 核心调用方法  
  
所有API调用都通过 `KingdeeDefaultOpClient.requestContent(...)` 方法发送请求。  
  
## API接口列表  
  
### 1. 客户管理相关接口  
  
#### 1.1 客户保存  
- **接口名称**: CustomerSaveRequest  
- **URL路径**: `/v2/gjwl/basedata/bd_customer/saveCustomer`  
- **日志模块**: `kd.bd_customer.saveCustomer`  
- **请求方法**: POST  
- **请求参数**: `List<Customer>`  
- **返回参数**: `CustomerSaveResponse`  
- **描述**: 保存客户信息  
  
#### 1.2 客户扩展保存  
- **接口名称**: CustomerSaveExtRequest  
- **URL路径**: `/v2/gjwl/cas/basedata/bd_customer/saveCustomer`  
- **日志模块**: `kd.bd_customer.saveCustomerExt`  
- **请求方法**: POST  
- **请求参数**: `List<Customer>`  
- **返回参数**: `CustomerSaveExtResponse`  
- **描述**: 扩展方式保存客户信息  
  
#### 1.3 客户反审核  
- **接口名称**: CustomerUnAuditRequest  
- **URL路径**: `/v2/gjwl/basedata/bd_customer/unAuditCustomer`  
- **日志模块**: `kd.bd_customer.unAuditCustomer`  
- **请求方法**: POST  
- **请求参数**: `CustomerUnAudit`  
- **返回参数**: `CustomerUnAuditResponse`  
- **描述**: 对客户进行反审核操作  
  
### 2. 供应商管理相关接口  
  
#### 2.1 供应商创建  
- **接口名称**: SupplierCreateRequest  
- **URL路径**: `/v2/basedata/bd_supplier/add`  
- **日志模块**: `kd.bd_supplier.add`  
- **请求方法**: POST  
- **请求参数**: `SupplierCreatePara`  
  - `number`: 供应商编码  
  - `name`: 供应商名称  
  - `entryLinkman`: 联系人分录  
  - `entryBank`: 银行信息分录  
  - `entryGroupStandard`: 分类标准  
  - `entryTax`: 税务资质  
- **返回参数**: `SimpleResponse`  
- **描述**: 创建新的供应商  
  
#### 2.2 供应商保存  
- **接口名称**: SupplierSaveRequest  
- **URL路径**: `/v2/gjwl/cas/basedata/bd_supplier/saveSupplier`  
- **日志模块**: `kd.bd_supplier.saveSupplier`  
- **请求方法**: POST  
- **请求参数**: `SupplierSavePara`  
  - `number`: 供应商编码  
  - `gjwlAppid`: 应用ID  
  - `name`: 供应商名称  
- **返回参数**: `SupplierSaveResponse`  
- **描述**: 保存供应商信息  
  
#### 2.3 供应商批量更新  
- **接口名称**: SupplierUpdateRequest  
- **URL路径**: `/v2/basedata/bd_supplier/batchUpdate`  
- **日志模块**: `kd.bd_supplier.batchUpdate`  
- **请求方法**: POST  
- **请求参数**: `List<SupplierUpdatePara>`  
- **返回参数**: `SimpleResponse`  
- **描述**: 批量更新供应商信息  
  
#### 2.4 供应商反审核  
- **接口名称**: SupplierUnAuditRequest  
- **URL路径**: `/v2/gjwl/basedata/bd_supplier/unAuditSupplier`  
- **日志模块**: `kd.bd_customer.unAuditSupplier`  
- **请求方法**: POST  
- **请求参数**: `UnAuditPara`  
- **返回参数**: `SupplierUnAuditResponse`  
- **描述**: 对供应商进行反审核操作  
  
### 3. 收款相关接口  
  
#### 3.1 收款单新增保存  
- **接口名称**: PaymentRequest  
- **URL路径**: `/v2/gjwl/cas/cas_recbill/saveRecBill`  
- **日志模块**: `kd.cas_recbill.saveRecBill`  
- **请求方法**: POST  
- **请求参数**: `List<PaymentPara>`  
  - `billNo`: 单据编号  
  - `bizDate`: 业务日期  
  - `payerType`: 付款单位类型  
  - `txtDescription`: 摘要  
  - `actRecAmt`: 收款金额  
  - `exchangeRate`: 汇率  
  - `localAmt`: 折本位币  
  - `payerName`: 付款单位名称  
  - `payerAcctBankNum`: 付款账户  
  - `settleTNumber`: 结算号  
  - `payer`: 付款单位ID  
  - `payerFormId`: 付款单位类型标识ID  
  - `payerAccFormId`: 付款账户类型标识ID  
  - `payerAcctBank`: 付款账户ID  
  - `billTypeId`: 单据类型ID  
  - `billTypeNumber`: 单据类型编码  
  - `payerBankName`: 付款银行  
  - `isAgent`: 代理收款  
  - `paymentMode`: 付款方式  
  - `isRefund`: 退款退票业务  
  - `isFullRefund`: 全额退款  
  - `exRateDate`: 汇率日期  
  - `entries`: 收款明细  
  - `entryEntities`: 单据体列表  
  - `infoEntries`: 流水信息列表  
  - `draftBills`: 结算号基础资料列表  
  - `orgId`: 收款组织ID  
  - `orgNumber`: 收款组织编码  
  - `payeeBankId`: 收款银行ID  
  - `payeeBankNumber`: 收款银行编码  
- **返回参数**: `PaymentResponse`  
- **描述**: 新增保存收款单  
- **参考文档**: https://dev.kingdee.com/open/detail/api/1758916852244810752  
  
#### 3.2 收款流水查询  
- **接口名称**: ReceiptRecordQueryRequest  
- **URL路径**: `/v2/gjwl/cas/transDetail/transDetailNewQuery`  
- **日志模块**: `kd.bd_receipt.receiptRecordQuery`  
- **请求方法**: POST  
- **请求参数**: `ReceiptRecordQueryParams`  
  - `compCode`: 公司编码  
- **返回参数**: `DetailQueryResponse`  
- **描述**: 查询收款流水信息  
- **标准格式**: false  
  
#### 3.3 收款流水认领  
- **接口名称**: ReceiptClaimParamsRequest  
- **URL路径**: `/v2/gjwl/cas/recClaimInterface/recClaimInterface`  
- **日志模块**: `kd.bd_receipt.receiptClaim`  
- **请求方法**: POST  
- **请求参数**: `ReceiptClaimParams`  
  - `compCode`: 公司编码  
  - `claimRevInforBean`: 认领信息  
    - `receivablesId`: 收款流水ID  
    - `receivablesNum`: 收款流水编号  
- **返回参数**: `ReceiptClaimResponse`  
- **描述**: 对收款流水进行认领操作  
- **标准格式**: false  
  
#### 3.4 取消认领  
- **接口名称**: CancelClaimRequest  
- **URL路径**: `/v2/gjwl/cas/cancelRecClaim/calcelrecclaiminterface`  
- **日志模块**: `kd.bd_receipt.cancelClaim`  
- **请求方法**: POST  
- **请求参数**: `CancelClaimParams`  
  - `compCode`: 公司编码  
  - `operateType`: 操作类型 (UNCLAIM取消认领, AUDIT认领复核)  
  - `receivablesId`: 收款流水ID  
  - `remark`: 操作原因  
  - `casRecBillId`: 金蝶收款单据ID  
- **返回参数**: `ReceiptClaimResponse`  
- **描述**: 取消收款流水认领  
- **标准格式**: false  
  
### 4. 付款相关接口  
  
#### 4.1 付款申请查询  
- **接口名称**: PayApplyQueryRequest  
- **URL路径**: `/v2/gjwl/ap/payApply/payApplyQuery`  
- **日志模块**: `kd.payApply.query`  
- **请求方法**: POST  
- **请求参数**: `PayApplyQueryParam`  
- **返回参数**: `PayApplyQueryResponse`  
- **描述**: 查询付款申请信息  
  
#### 4.2 付款申请自动创建  
- **接口名称**: PayApplyAutoCreateRequest  
- **URL路径**: `/v2/gjwl/ap/payApply/payApplyAutoCreate`  
- **日志模块**: `kd.payApply.create`  
- **请求方法**: POST  
- **请求参数**: `List<Claim>`  
- **返回参数**: `PayApplyAutoCreateResponse`  
- **描述**: 自动创建付款申请  
  
#### 4.3 金蝶付款单新增  
- **接口名称**: AddKdPaymentRequest  
- **URL路径**: `/v2/gjwl/cas/cas_paybill/savePayBill`  
- **日志模块**: `kd.cas_paybill.addSave`  
- **请求方法**: POST  
- **请求参数**: `List<AddKdPaymentPara>`  
  - `billno`: 单据编号  
  - `bizdate`: 业务日期  
  - `entry`: 分录  
  - `payeetype`: 收款单位类型  
  - `description`: 描述  
- **返回参数**: `AddKdPaymentResponse`  
- **描述**: 新增金蝶付款单  
  
#### 4.4 查询报账单状态
- **接口名称**: ClaimQueryRequest
- **URL路径**: `/v2/gjwl/cas/billSatus/billstatusquery`
- **日志模块**: `kd.cas.billSatus.billStatusQuery`
- **请求方法**: POST
- **请求参数**: `ClaimQueryPara`
  - `claimNos`: 报账单号
- **返回参数**: `PayLineResultResponse`
- **描述**: 查询报账单状态
- **标准格式**: false

### 5. 项目管理相关接口

#### 5.1 项目保存提交审核
- **接口名称**: ProjectSaveRequest
- **URL路径**: `/v2/gjwl/basedata/bd_project/saveProject`
- **日志模块**: `kd.bd_project.saveProject`
- **请求方法**: POST
- **请求参数**: `List<Project>`
  - `id`: 项目ID (修改时需传)
  - `number`: 项目编码 (必填)
  - `name`: 项目名称 (必填)
  - `createorg_number`: 创建组织编码 (必填)
  - `gjwl_appid`: 外部编码 (必填)
  - `gjwl_sourcesystemtype`: 来源系统 (必填, 固定值"广交云供应链管理系统")
- **返回参数**: `ProjectSaveResponse`
- **描述**: 广交云项目审核后推送金蝶云星空旗舰版项目信息

#### 5.2 项目反审核
- **接口名称**: ProjectUnAuditRequest
- **URL路径**: `/v2/basedata/bd_project/batchUnaudit`
- **日志模块**: `kd.bd_project.unAuditProject`
- **请求方法**: POST
- **请求参数**: `ProjectUnAudit`
  - `id`: 项目ID列表 (必填, Array<String>)
- **返回参数**: `ProjectUnAuditResponse`
- **描述**: 广交云项目审核后需修改保存前需反审核金蝶云星空旗舰版项目信息

### 6. 物料管理相关接口

#### 6.1 物料和组织公共信息批量保存提交审核
- **接口名称**: MaterialSaveRequest
- **URL路径**: `/v2/gjwl/gjwl_basedata_ext/saveMaterial`
- **日志模块**: `kd.bd_material.saveMaterial`
- **请求方法**: POST
- **请求参数**: `MaterialSaveRequest.MaterialSaveData`
  - `materials`: 物料数据列表 (`List<MaterialDataVo>`)
    - `materialInfoVo`: 物料主档信息
      - `id`: 物料ID
      - `name`: 物料名称 (必填)
      - `number`: 物料编码
      - `gjwl_appid`: 外部编码 (必填)
      - `createorg`: 创建组织编码 (固定值"gjwl")
      - `baseunit`: 基本单位编码 (必填)
      - `modelnum`: 规格型号
      - `unitconvertdir`: 换算方向 (固定值"A")
      - `entry_groupstandard`: 分类信息分录
    - `commonInfoVo`: 物料组织公共信息
      - `materialtype`: 物料类型 (固定值"1")
      - `materialattr`: 物料属性 (固定值"10040")
      - `enablepur`: 采购信息 (固定值true)
      - `enablesale`: 销售信息 (固定值true)
      - `enableinv`: 库存信息 (固定值true)
      - `group`: 存货类别 (固定值"CHJZFL06-SYS")
- **返回参数**: `MaterialSaveResponse`
- **描述**: 广交云产品审核后推送金蝶云星空旗舰版物料信息

#### 6.2 物料库存信息批量更新
- **接口名称**: MaterialInventoryUpdateRequest
- **URL路径**: `/v2/sbd/bd_materialinventoryinfo/batchUpdate`
- **日志模块**: `kd.bd_material.inventoryUpdate`
- **请求方法**: POST
- **请求参数**: `List<MaterialInventoryUpdatePara>`
  - `enableshelflifemgr`: 保质期管理 (必填)
  - `shelflifeunit`: 保质期单位 (必填, day/month/year)
  - `shelflife`: 保质期
  - `caldirection`: 计算方向 (固定值"4")
  - `startdatecaltype`: 生产日计算方式 (固定值"1")
  - `calculationforenddate`: 到期日计算方式 (固定值"0")
  - `leadtimeunit`: 提前期单位 (必填, day/month/year)
  - `masterid_number`: 物料编码 (必填)
  - `createorg_number`: 库存信息创建组织编码 (固定值"gjwl")
- **返回参数**: `MaterialInventoryUpdateResponse`
- **描述**: 广交云产品新增后，如物料需要开启保质期则调用该接口

## 通用响应格式
  
### 标准响应格式  
```json  
{  
  "errorCode": "0",  
  "message": "成功",  
  "status": true,  
  "data": {}  
}  
```  
  
### 错误码说明  
- `0`: 成功  
- `400`: 参数检查错误  
- `401`: Token检查签名错误  
- `403`: 禁止访问错误  
- `404`: API错误  
- `601`: 数据重复错误  
- `999`: 未识别异常  
  
## 请求头信息  
- `Content-Type`: application/json  
- `accesstoken`: 访问令牌  
- `x-acgw-identity`: 身份标识  
- `Idempotency-Key`: 幂等性键值(UUID)  
  
## 注意事项  
1. 所有请求都需要有效的访问令牌  
2. 请求体格式为JSON  
3. 部分接口支持标准格式包装，部分不支持  
4. 所有接口都会记录详细的日志信息  
5. 支持超时设置：连接超时60秒，读取超时60秒，写入超时60秒